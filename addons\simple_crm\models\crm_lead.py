from odoo import models, fields, api


class CrmLead(models.Model):
    _inherit = 'crm.lead'

    # Simple additional fields for CRM leads
    lead_priority = fields.Selection([
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ], string='Priority Level', default='medium')
    
    budget_range = fields.Selection([
        ('small', 'Small (< $10K)'),
        ('medium', 'Medium ($10K - $50K)'),
        ('large', 'Large ($50K - $100K)'),
        ('enterprise', 'Enterprise (> $100K)'),
    ], string='Budget Range')
    
    decision_maker = fields.Char(
        string='Decision Maker',
        help='Name of the decision maker'
    )
    
    competitor = fields.Char(
        string='Main Competitor',
        help='Main competitor for this opportunity'
    )
    
    next_action = fields.Text(
        string='Next Action',
        help='Next steps to move the opportunity forward'
    )
    
    lead_source_detail = fields.Char(
        string='Lead Source Detail',
        help='Additional details about the lead source'
    )
    
    # Computed field for days since creation
    days_old = fields.Integer(
        string='Days Old',
        compute='_compute_days_old'
    )
    
    @api.depends('create_date')
    def _compute_days_old(self):
        today = fields.Date.today()
        for lead in self:
            if lead.create_date:
                create_date = lead.create_date.date()
                delta = today - create_date
                lead.days_old = delta.days
            else:
                lead.days_old = 0
    
    def action_mark_as_hot(self):
        """Mark lead as hot/urgent"""
        self.lead_priority = 'urgent'
        self.message_post(body="Lead marked as HOT - urgent follow-up required!")
    
    def action_schedule_call(self):
        """Schedule a call activity"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Schedule Call',
            'res_model': 'mail.activity',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_res_id': self.id,
                'default_res_model': 'crm.lead',
                'default_activity_type_id': self.env.ref('mail.mail_activity_data_call').id,
                'default_summary': f'Call regarding {self.name}',
            }
        }
